"""
Story Discovery Phase implementation for DynamicResearchCoordinator.

This module implements Phase 1 of the story-centric workflow: topic analysis
and parallel story discovery using WideSearchAgents.

Following PRD.md architecture:
- Analyzes user requirements to identify topic areas using proper ADK agent patterns
- Creates parallel WideSearchAgents for each topic area
- Aggregates discovered stories into session state

Key Features:
- ADK-native ParallelAgent coordination
- LLM-driven topic area identification using proper LlmAgent instances
- Universal flexibility for any newsletter type
- Explicit error handling with clear ValueError messages
- Session state management using PRD-defined keys
- Self-contained phase without external planning dependencies

Anti-Pattern Prevention:
- No hardcoded topic areas (trusts LLM intelligence)
- No custom agent wrappers (uses existing agents directly)
- No silent fallbacks (explicit error handling)
- No newsletter-specific logic (universal flexibility)
- No direct LLM API calls (uses proper ADK agent patterns)
- No external research planner dependencies (self-contained)
"""

from typing import AsyncGenerator
from google.adk.agents import ParallelAgent, LlmAgent
from google.adk.events import Event, EventActions

from ..wide_search_agent import exa_agent as wide_search_agent
from ...tools.litellm_tools import llm


class StoryDiscoveryPhase:
    """
    Phase 1: Story Discovery through topic analysis and parallel search.

    This class implements the story discovery phase of the story-centric workflow,
    using LLM intelligence to identify topic areas and coordinating parallel
    WideSearchAgents to discover relevant stories.
    """

    def __init__(self, coordinator_name: str = "DynamicResearchCoordinator"):
        """Initialize the StoryDiscoveryPhase with proper ADK agent instances."""
        self.coordinator_name = coordinator_name

        # Create proper LlmAgent for topic analysis (ADK-native pattern)
        self.topic_analysis_agent = LlmAgent(
            name="TopicAnalysisAgent",
            model=llm,
            instruction="""
            You are a topic analysis specialist that identifies key topic areas for newsletter story discovery.

            Your task is to analyze natural language newsletter requirements and identify 3-5 distinct
            topic areas that should be researched for story discovery.

            CRITICAL: Work directly with the user's natural language requirements. Identify topic areas
            that are:
            - Specific to the user's newsletter type and audience
            - Broad enough to capture relevant stories
            - Distinct from each other to avoid overlap
            - Appropriate for the user's stated requirements

            RESPONSE FORMAT:
            Return only a simple list of topic areas, one per line, without numbering or bullets.

            Example for a biotech VC newsletter focused on TL1A:
            TL1A research developments
            TL1A Clinical trial updates
            TL1A Biotech company funding
            TL1A Regulatory changes
            TL1A Market analysis

            Example for a daily tech newsletter:
            AI and machine learning trends
            Software development trends
            Tech industry news
            Tech startup funding
            Tech product launches

            Trust your intelligence to identify the most relevant topic areas for the specific
            newsletter requirements provided.
            """,
            tools=[],
            output_key="topic_areas_raw"
        )

    async def execute(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Execute Phase 1: Story Discovery.

        Args:
            ctx: ADK context with session state

        Yields:
            Event: ADK events with state updates

        Raises:
            ValueError: If topic identification fails or required inputs are missing
        """
        # Read from session state (ADK pattern) - REMOVE research_plan dependency
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Validate inputs (explicit error handling - MANDATORY)
        if not user_requirements:
            raise ValueError("No user requirements provided for story discovery")

        # Use proper LlmAgent for topic analysis (ADK-native pattern)
        async for event in self.topic_analysis_agent.run_async(ctx):
            yield event

        # Extract topic areas from agent output
        topic_areas_raw = ctx.session.state.get("topic_areas_raw", "")
        if not topic_areas_raw:
            raise ValueError("Topic analysis agent failed to produce output")

        # Parse topic areas from LLM response (simple line-by-line parsing)
        topic_areas = [line.strip() for line in topic_areas_raw.split('\n') if line.strip()]

        if not topic_areas:
            raise ValueError("No topic areas identified from user requirements")

        # Save topic areas to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name,
            actions=EventActions(state_delta={"topic_areas": topic_areas})
        )

        # Create parallel WideSearchAgents for each topic area
        # Use existing agents directly (no custom wrappers)
        # Note: For initial implementation, we'll use a single agent instance
        # In future iterations, this would create separate agent instances per topic
        parallel_search = ParallelAgent(name="TopicSearch", sub_agents=[wide_search_agent])

        # Execute parallel topic discovery
        async for event in parallel_search.run_async(ctx):
            yield event

        # Aggregate discovered stories
        discovered_stories = await self._aggregate_stories(ctx)

        # Save discovered stories to session state (PRD-defined key)
        yield Event(
            author=self.coordinator_name,
            actions=EventActions(state_delta={"discovered_stories": discovered_stories})
        )



    async def _aggregate_stories(self, ctx) -> list[dict]:
        """
        Aggregate stories from parallel search results.

        This method collects and aggregates story discoveries from the parallel
        WideSearchAgents. For the initial implementation, this provides a simple
        aggregation that can be enhanced in future iterations.

        Args:
            ctx: ADK context with session state

        Returns:
            list[dict]: Aggregated stories from all topic searches
        """
        # Get wide search results from session state
        wide_search_results = ctx.session.state.get("wide_search_results", [])

        # Simple aggregation for initial implementation
        # In future iterations, this would:
        # - Collect results from each WideSearchAgent
        # - Deduplicate similar stories
        # - Rank by relevance to user requirements
        # - Format consistently for next phase

        # For now, return the wide search results directly
        # This will be enhanced when implementing the full story aggregation logic
        if isinstance(wide_search_results, list):
            return wide_search_results
        else:
            # Handle case where results might be a string or other format
            return []
